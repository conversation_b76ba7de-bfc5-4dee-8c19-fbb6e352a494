
#ifndef __ASM_MODULE_H
#define __ASM_MODULE_H

#include <elf.h>
#include <limits.h>
#include <stdbool.h>
#ifndef CONFIG_OS_LP64
typedef long long s64;          /* 有符号64bit */
typedef unsigned long long u64; /* 无符号64bit */
#else
typedef long s64;          /* 有符号64bit */
typedef unsigned long u64; /* 无符号64bit */
#endif
/*
 * Many architectures just need a simple module
 * loader without arch specific data.
 */
#define CONFIG_HAVE_MOD_ARCH_SPECIFIC
#ifndef CONFIG_HAVE_MOD_ARCH_SPECIFIC
struct mod_arch_specific
{
};
#else
struct mod_plt_sec {
	Elf64_Shdr	*plt;
	int			plt_num_entries;
	int			plt_max_entries;
};

struct mod_arch_specific {
	struct mod_plt_sec	core;
	struct mod_plt_sec	init;

	/* for CONFIG_DYNAMIC_FTRACE */
	//struct plt_entry 	*ftrace_trampoline;
};
#endif

#if ULONG_MAX == 0xffffffffUL
typedef Elf32_Ehdr Elf_Ehdr;
typedef Elf32_Phdr Elf_Phdr;
typedef Elf32_Shdr Elf_Shdr;
typedef Elf32_Sym Elf_Sym;
typedef Elf32_Dyn Elf_Dyn;
typedef Elf32_Addr Elf_Addr;
typedef Elf32_Rel Elf_Rel;
typedef Elf32_Rela Elf_Rela;
typedef Elf32_auxv_t aux_t;

#define ELF_R_TYPE(X) ELF32_R_TYPE(X)
#define ELF_R_SYM(X) ELF32_R_SYM(X)

#else
typedef Elf64_Ehdr Elf_Ehdr;
typedef Elf64_Phdr Elf_Phdr;
typedef Elf64_Shdr Elf_Shdr;
typedef Elf64_Sym Elf_Sym;
typedef Elf64_Dyn Elf_Dyn;
typedef Elf64_Addr Elf_Addr;
typedef Elf64_Rel Elf_Rel;
typedef Elf64_Rela Elf_Rela;
typedef Elf64_auxv_t aux_t;

#define ELF_R_TYPE(X) ELF64_R_TYPE(X)
#define ELF_R_SYM(X) ELF64_R_SYM(X)

#endif

#define ELF_ST_BIND(x) (((unsigned char)(x)) >> 4)
#define ELF_ST_TYPE(x) (((unsigned int)x) & 0xf)

#define elf_check_arch(x) ((x)->e_machine == EM_AARCH64)

//放到libk目录下的elf.h更合适
#define SHN_LIVEPATCH 0xff20
#define SHF_RO_AFTER_INIT 0x00200000
#define SHF_RELA_LIVEPATCH 0x00100000

static inline int is_forbidden_offset_for_adrp(void *place)
{
    /* nuttx的重定位代码中,((u64)place & 0xfff) >= 0xff8 不满足,就返回0
       而linux的重定位代码中,这三个条件有一个不满足,就返回0
    return IS_ENABLED(CONFIG_ARM64_ERRATUM_843419) &&
           cpus_have_const_cap(ARM64_WORKAROUND_843419) &&
           ((u64)place & 0xfff) >= 0xff8;
       采用nuttx的实现
    */
    return ((u64)place & 0xfff) >= 0xff8;
}

struct plt_entry
{
	/*
	 * A program that conforms to the AArch64 Procedure Call Standard
	 * (AAPCS64) must assume that a veneer that alters IP0 (x16) and/or
	 * IP1 (x17) may be inserted at any branch instruction that is
	 * exposed to a relocation that supports long branches. Since that
	 * is exactly what we are dealing with here, we are free to use x16
	 * as a scratch register in the PLT veneers.
	 */
	uint32_t	mov0;	/* movn	x16, #0x....			*/
	uint32_t	mov1;	/* movk	x16, #0x...., lsl #16		*/
	uint32_t	mov2;	/* movk	x16, #0x...., lsl #32		*/
	uint32_t	br;	/* br	x16				*/
};

static inline struct plt_entry get_plt_entry(u64 val)
{
    //todo小端定义
    #define cpu_to_le16(x)  (x)
    #define cpu_to_le32(x)  (x)
	/*
	 * MOVK/MOVN/MOVZ opcode:
	 * +--------+------------+--------+-----------+-------------+---------+
	 * | sf[31] | opc[30:29] | 100101 | hw[22:21] | imm16[20:5] | Rd[4:0] |
	 * +--------+------------+--------+-----------+-------------+---------+
	 *
	 * Rd     := 0x10 (x16)
	 * hw     := 0b00 (no shift), 0b01 (lsl #16), 0b10 (lsl #32)
	 * opc    := 0b11 (MOVK), 0b00 (MOVN), 0b10 (MOVZ)
	 * sf     := 1 (64-bit variant)
	 */
	return (struct plt_entry){
		cpu_to_le32(0x92800010 | (((~val      ) & 0xffff)) << 5),
		cpu_to_le32(0xf2a00010 | ((( val >> 16) & 0xffff)) << 5),
		cpu_to_le32(0xf2c00010 | ((( val >> 32) & 0xffff)) << 5),
		cpu_to_le32(0xd61f0200)
	};
    #undef cpu_to_le16
    #undef cpu_to_le32
}

static inline bool plt_entries_equal(const struct plt_entry *a,
				     const struct plt_entry *b)
{
	return a->mov0 == b->mov0 &&
	       a->mov1 == b->mov1 &&
	       a->mov2 == b->mov2;
}


#endif /* __ASM_MODULE_H */
