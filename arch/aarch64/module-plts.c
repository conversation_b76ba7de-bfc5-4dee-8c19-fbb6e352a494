/**
 * @file    arch/aarch64/module.c
 * <AUTHOR> @brief   实现加载内核模块长跳转
 * @version 3.0.0
 * @date    2025-07-30
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/* @<MODULE */
#ifdef CONFIG_MODULES
/************************头 文 件******************************/
#define KLOG_TAG "module-plts"
#include <klog.h>
#include <asm_module.h>
#include <system/kconfig.h>
#include <system/bitops.h>
#include <module.h>

/************************宏 定 义******************************/
#define cmp_3way(a,b)	((a) < (b) ? -1 : (a) > (b))

#define SZ_1K 0x00000400
#define SZ_2K 0x00000800
#define SZ_4K 0x00001000
#define SZ_8K 0x00002000
#define SZ_16K 0x00004000
#define SZ_32K 0x00008000
#define SZ_64K 0x00010000
#define SZ_128K 0x00020000
#define SZ_256K 0x00040000
#define SZ_512K 0x00080000
/************************类型定义******************************/
/************************外部声明******************************/
/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实   现*******************************/

/**
 * @brief 判断一个内存地址loc是否位于模块的init区域内。
 * @param mod 指向模块的结构体
 * @param loc 要检查的内存地址
 * @return bool 如果地址在init区域内，则返回true
 */
static bool in_init(const struct module *mod, void *loc)
{
	// 通过计算地址loc相对于模块init区域基地址的偏移量，
	// 并判断该偏移量是否小于init区域的大小，来确定loc是否在init区域内。
	return (u64)loc - (u64)mod->init_layout.base < mod->init_layout.size;
}

/**
 * @brief 为一个长跳转动态生成一个PLT条目（跳板）。
 * @param mod 当前模块
 * @param loc 需要修正的BL/B（跳转）指令的地址
 * @param rela 描述如何修正的重定位条目
 * @param sym 目标符号（函数）
 * @return u64 返回新创建的或可重用的PLT条目的地址
 */
u64 module_emit_plt_entry(struct module *mod, void *loc, const Elf64_Rela *rela,
			  Elf64_Sym *sym)
{
	// 根据调用点loc的位置，判断应该使用模块的“核心(core)”PLT还是“初始化(init)”PLT。
	struct mod_plt_sec *pltsec = !in_init(mod, loc) ? &mod->arch.core :
							  &mod->arch.init;
	// 获取PLT段的内存起始地址。
	struct plt_entry *plt = (struct plt_entry *)pltsec->plt->sh_addr;
	// i是当前PLT段中已有的条目数量，也是新条目的索引。
	int i = pltsec->plt_num_entries;
	// 计算出目标函数的最终绝对地址 (符号的基地址 + 重定位条目中的加数)。
	u64 val = sym->st_value + rela->r_addend;

	// 调用一个辅助函数，根据目标地址val，动态生成PLT条目的机器码。
	plt[i] = get_plt_entry(val);

	/*
	 * 优化：检查我们刚刚创建的条目是否与上一个重复。因为
	 * 重定位信息是排过序的，所以如果存在重复，它一定是前一个。
	 * (如果存在的话)。
	 */
	if (i > 0 && plt_entries_equal(plt + i, plt + i - 1))
		// 如果重复，就直接返回上一个条目的地址，实现复用
		return (u64)&plt[i - 1];

	// 如果不重复，则增加PLT条目计数。
	pltsec->plt_num_entries++;
	// 检查是否超出了预先分配的PLT空间。如果超出，说明之前的空间计算有误，触发内核警告。
	if (WARN_ON(pltsec->plt_num_entries > pltsec->plt_max_entries))
		return 0;

	// 返回新创建的PLT条目的地址。这个地址会被用来修正原始的BL指令。
	return (u64)&plt[i];
}

#ifdef CONFIG_ARM64_ERRATUM_843419
/**
 * @brief 为ADRP指令动态生成一个veneer（垫片/跳板），以规避硬件勘误843419。
 * @param mod 当前模块
 * @param loc 有问题的ADRP指令的地址
 * @param val ADRP指令想要计算的目标地址
 * @return u64 返回新创建的veneer的地址
 */
u64 module_emit_veneer_for_adrp(struct module *mod, void *loc, u64 val)
{
	// 同样，先确定使用core还是init部分的PLT空间来存放veneer。
	struct mod_plt_sec *pltsec = !in_init(mod, loc) ? &mod->arch.core :
							  &mod->arch.init;
	// 获取PLT段的起始地址。
	struct plt_entry *plt = (struct plt_entry *)pltsec->plt->sh_addr;
	// 获取新veneer的索引，并立即增加计数。
	int i = pltsec->plt_num_entries++;
	u32 mov0, mov1, mov2, br;
	int rd;

	// 检查空间是否用尽。
	if (WARN_ON(pltsec->plt_num_entries > pltsec->plt_max_entries))
		return 0;

	/* 从原始ADRP指令的机器码中，解码出它的目标寄存器（rd）。*/
	rd = aarch64_insn_decode_register(AARCH64_INSN_REGTYPE_RD,
					  le32_to_cpup((__le32 *)loc));

	/* 动态生成veneer的指令机器码 */
	// 生成一系列指令，用来将一个完整的64位地址val加载到目标寄存器rd中。
	mov0 = aarch64_insn_gen_movewide(rd, (u16)~val, 0,
					 AARCH64_INSN_VARIANT_64BIT,
					 AARCH64_INSN_MOVEWIDE_INVERSE);
	mov1 = aarch64_insn_gen_movewide(rd, (u16)(val >> 16), 16,
					 AARCH64_INSN_VARIANT_64BIT,
					 AARCH64_INSN_MOVEWIDE_KEEP);
	mov2 = aarch64_insn_gen_movewide(rd, (u16)(val >> 32), 32,
					 AARCH64_INSN_VARIANT_64BIT,
					 AARCH64_INSN_MOVEWIDE_KEEP);
	// 生成一条无条件跳转指令，跳回到原始ADRP指令的下一条指令处。
	br = aarch64_insn_gen_branch_imm((u64)&plt[i].br, (u64)loc + 4,
					 AARCH64_INSN_BRANCH_NOLINK);

	// 将这些动态生成的指令机器码填充到PLT条目中。
	plt[i] = (struct plt_entry){
			cpu_to_le32(mov0),
			cpu_to_le32(mov1),
			cpu_to_le32(mov2),
			cpu_to_le32(br)
		};

	// 返回这个veneer的地址。
	return (u64)&plt[i];
}
#endif

/**
 * @brief sort函数需要的回调函数，用于比较两个Elf64_Rela重定位条目。
 */
static int cmp_rela(const void *a, const void *b)
{
	const Elf64_Rela *x = a, *y = b;
	int i;

	/* 排序规则：首先按重定位类型，然后按符号索引，最后按加数。*/
	i = cmp_3way(ELF64_R_TYPE(x->r_info), ELF64_R_TYPE(y->r_info));
	if (i == 0) // 如果类型相同，则比较符号索引
		i = cmp_3way(ELF64_R_SYM(x->r_info), ELF64_R_SYM(y->r_info));
	if (i == 0) // 如果符号也相同，则比较加数
		i = cmp_3way(x->r_addend, y->r_addend);
	return i;
}

/**
 * @brief 检查一个重定位条目是否与前一个重复。
 */
static bool duplicate_rel(const Elf64_Rela *rela, int num)
{
	/*
	 * 因为条目已经根据类型、符号和加数排好序，这意味着
	 * 如果存在一个重复的条目，它必然是紧邻的前一个。
	 */
	return num > 0 && cmp_rela(rela + num, rela + num - 1) == 0;
}

/**
 * @brief 遍历一个节的所有重定位条目，计算总共需要多少个PLT条目或Veneer。
 */
static unsigned int count_plts(Elf64_Sym *syms, Elf64_Rela *rela, int num,
			       Elf64_Word dstidx, Elf_Shdr *dstsec)
{
	unsigned int ret = 0; // PLT/Veneer条目计数器
	Elf64_Sym *s;
	int i;

	// 遍历这个节的所有重定位条目
	for (i = 0; i < num; i++) {
		u64 min_align;

		// 根据重定位的类型进行判断
		switch (ELF64_R_TYPE(rela[i].r_info)) {
		case R_AARCH64_JUMP26:
		case R_AARCH64_CALL26:
			// // 如果内核地址随机化功能未启用，则不需要PLT
			// if (!IS_ENABLED(CONFIG_RANDOMIZE_BASE))
			// 	break;

			/*
			 * 我们只需要考虑那些跳转目标符号定义在不同节的跳转。
			 * 这不仅仅是一个启发式优化，更是一个根本性的限制，因为如果节本身的大小
			 * 超过了跳转指令的范围，我们无法保证生成的PLT条目离调用点足够近。
			 * 所以，如果重定位的目标符号与调用点在同一个节内，就忽略它。
			 */
			s = syms + ELF64_R_SYM(rela[i].r_info);
			if (s->st_shndx == dstidx)
				break;

			/*
			 * ELF规范支持对未定义符号进行带非零加数的跳转重定位（例如，
			 * ‘跳转到未定义函数f入口点之后n个字节处’），但这在实践中
			 * 不会发生。所以我们需要支持它，但在优化代码时无需考虑。
			 * 因此我们只在加数为零时才检查重复：这允许我们将PLT条目
			 * 的地址记录在符号表本身，而不是每次生成时都去搜索列表。
			 */
			if (rela[i].r_addend != 0 || !duplicate_rel(rela, i))
				ret++; // 如果不重复，PLT计数器加一
			break;
		case R_AARCH64_ADR_PREL_PG_HI21_NC:
		case R_AARCH64_ADR_PREL_PG_HI21:
			// 如果内核未配置规避勘误843419，或者CPU没有这个缺陷，则跳过
			if (!IS_ENABLED(CONFIG_ARM64_ERRATUM_843419))
				break;

			/*
			 * 为这条ADRP指令确定最小的安全对齐值：即保证它
			 * 不会出现在有风险偏移量上的节对齐值。
			 *
			 * 这归结为在节偏移的[11:3]位中找到最低有效位的0，
			 * 并增加节的对齐值，以保证这条指令的最终地址
			 * 在该位以及所有更低有效位上都等于偏移量。这确保了
			 * 地址模4KB不等于0xfff8或0xfffc（这两种情况[11:3]位全为1）。
			 */
			min_align = 2ULL << ffz(rela[i].r_offset | 0x7);

			/*
			 * 为每一个可能出现在有风险偏移量上的ADRP指令预留veneer空间。
			 * 在重定位时，其中一些可能不会被使用，因为某些ADRP指令
			 * 可以被修正为ADR指令来代替。
			 */
			if (min_align > SZ_4K)
				ret++; // 如果安全对齐值太大，只能用veneer，计数器加一
			else
				// 否则，尝试增加整个节的对齐要求
				dstsec->sh_addralign = max(dstsec->sh_addralign,
							   min_align);
			break;
		}
	}
	return ret; // 返回总共需要的PLT/Veneer条目数量
}

/**
 * @brief 在模块加载的早期阶段，预处理ELF节头，计算并分配PLT空间。
 */
int elfmodule_frob_arch_sections(Elf_Ehdr *ehdr, Elf_Shdr *sechdrs,
			      char *secstrings, struct module *mod)
{
	unsigned long core_plts = 0;
	unsigned long init_plts = 0;
	Elf64_Sym *syms = NULL;
	Elf_Shdr *tramp = NULL;
	int i;

	/*
	 * 遍历所有节头，找到空的.plt节，以便可以扩展它来存储PLT条目。
	 * 同时记录符号表的地址。
	 */
	for (i = 0; i < ehdr->e_shnum; i++)
    {
		if (!strcmp(secstrings + sechdrs[i].sh_name, ".plt"))
			mod->arch.core.plt = sechdrs + i;
		else if (!strcmp(secstrings + sechdrs[i].sh_name, ".init.plt"))
			mod->arch.init.plt = sechdrs + i;
		else if (IS_ENABLED(CONFIG_DYNAMIC_FTRACE) &&
			 !strcmp(secstrings + sechdrs[i].sh_name,
				 ".text.ftrace_trampoline"))
			tramp = sechdrs + i;
		else if (sechdrs[i].sh_type == SHT_SYMTAB)
			syms = (Elf64_Sym *)sechdrs[i].sh_addr;
	}

	/* 检查是否找到了必要的节 */
	if (!mod->arch.core.plt || !mod->arch.init.plt)
    {
		KLOG_E("%s: module PLT section(s) missing\n", mod->name);
		return -ENOEXEC;
	}
	if (!syms)
    {
		KLOG_E("%s: module symtab section missing\n", mod->name);
		return -ENOEXEC;
	}

	// 再次遍历所有节，专门处理重定位节 (SHT_RELA类型)
	for (i = 0; i < ehdr->e_shnum; i++)
    {
		Elf64_Rela *rels = (void *)ehdr + sechdrs[i].sh_offset;
		int numrels = sechdrs[i].sh_size / sizeof(Elf64_Rela);
		Elf64_Shdr *dstsec = sechdrs + sechdrs[i].sh_info;

		if (sechdrs[i].sh_type != SHT_RELA)
			continue;

		/* 忽略那些不对可执行代码节进行重定位的记录 */
		if (!(dstsec->sh_flags & SHF_EXECINSTR))
			continue;

		/*  对每个重定位节中的条目进行排序，以便于后续去重和优化。*/
		qsort(rels, numrels, sizeof(Elf64_Rela), cmp_rela);

		// 调用count_plts函数，计算这个节需要多少个PLT条目或Veneer。
		if (strncmp(secstrings + dstsec->sh_name, ".init", 5) != 0)
			// 如果不属于init节，就计入core_plts
			core_plts += count_plts(syms, rels, numrels,
						sechdrs[i].sh_info, dstsec);
		else
			// 否则计入init_plts
			init_plts += count_plts(syms, rels, numrels,
						sechdrs[i].sh_info, dstsec);
	}

    //todo目前手动定义L1_CACHE_BYTES
    #define L1_CACHE_SHIFT		(6)
    #define L1_CACHE_BYTES		(1 << L1_CACHE_SHIFT)
	// 根据计算出的总数，更新.plt和.init.plt节的大小和属性,为PLT动态地“扩容”准备好足够的内存空间
	mod->arch.core.plt->sh_type = SHT_NOBITS; // .plt节在文件中不占空间，在加载时才分配
	mod->arch.core.plt->sh_flags = SHF_EXECINSTR | SHF_ALLOC; // 标记为可执行、需分配内存
	mod->arch.core.plt->sh_addralign = L1_CACHE_BYTES; // 按L1缓存行对齐
	mod->arch.core.plt->sh_size = (core_plts  + 1) * sizeof(struct plt_entry); // 计算总大小
	mod->arch.core.plt_num_entries = 0; // 初始化已用条目数为0
	mod->arch.core.plt_max_entries = core_plts; // 记录最大可用条目数

	// 对init部分的.plt节做同样的操作
	mod->arch.init.plt->sh_type = SHT_NOBITS;
	mod->arch.init.plt->sh_flags = SHF_EXECINSTR | SHF_ALLOC;
	mod->arch.init.plt->sh_addralign = L1_CACHE_BYTES;
	mod->arch.init.plt->sh_size = (init_plts + 1) * sizeof(struct plt_entry);
	mod->arch.init.plt_num_entries = 0;
	mod->arch.init.plt_max_entries = init_plts;

	// 如果存在ftrace动态追踪的trampoline节，也为其设置属性
	if (tramp)
    {
		tramp->sh_type = SHT_NOBITS;
		tramp->sh_flags = SHF_EXECINSTR | SHF_ALLOC;
		tramp->sh_addralign = __alignof__(struct plt_entry);
		tramp->sh_size = sizeof(struct plt_entry);
	}
    KLOG_D("plt=0x%x, init.plt=0x%x plt max entries=0x%x init.plt max entries=0x%x", mod->arch.core.plt->sh_size,
           mod->arch.init.plt->sh_size, mod->arch.core.plt_max_entries, mod->arch.init.plt_max_entries);
	return 0;
}
#endif
