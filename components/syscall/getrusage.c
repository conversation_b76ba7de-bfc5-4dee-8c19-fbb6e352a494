/**
 * @file getrusage.c
 * <AUTHOR> (zhang<PERSON><EMAIL>)
 * @brief 获取进程资源使用统计信息
 * @version 3.0.0
 * @date 2024-11-14
 *
 * @ingroup syscall
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include "syscall_internal.h"
#include <time/ktime.h>
#include <ttosProcess.h>
#include <uaccess.h>

/**
 * @brief 获取内存区域的大小。
 *
 * 该函数用于计算内存区域的大小，并累加到资源使用统计中。
 *
 * @param[in] region 内存区域结构体
 * @param[in,out] param 资源使用统计结构体
 */
static void __region_getsize(struct mm *mm, struct mm_region *region, void *param)
{
    struct rusage *ru = (struct rusage *)param;
    ru->ru_maxrss += region->region_page_count << PAGE_SIZE_SHIFT;
}

/**
 * @brief 获取任务的资源使用统计。
 *
 * 该函数用于获取单个任务的CPU时间使用情况。
 *
 * @param[in] pcb 进程控制块
 * @param[in,out] param 资源使用统计结构体
 */
static void __task_getrusage(pcb_t pcb, void *param)
{
    struct rusage *ru = (struct rusage *)param;
    ru->ru_utime.tv_sec += pcb->utime.tv_sec;
    ru->ru_utime.tv_usec += pcb->utime.tv_nsec / NSEC_PER_USEC;
    ru->ru_stime.tv_sec += pcb->stime.tv_sec;
    ru->ru_stime.tv_usec += pcb->stime.tv_nsec / NSEC_PER_USEC;
}

/**
 * @brief 获取进程的资源使用统计。
 *
 * 该函数用于获取进程及其所有线程的资源使用情况。
 *
 * @param[in] pcb 进程控制块
 * @param[in,out] param 资源使用统计结构体
 */
static void __process_getrusage(pcb_t pcb, void *param)
{
    if (!pcb_get(pcb))
    {
        return;
    }
    foreach_task_group(pcb, __task_getrusage, param);
    mm_foreach_region(get_process_mm(pcb), __region_getsize, param);
    pcb_put(pcb);
}

/**
 * @brief 系统调用实现：获取进程资源使用统计信息。
 *
 * 该函数实现了一个系统调用，用于获取指定进程或线程的资源使用统计信息，
 * 包括CPU时间、内存使用等。
 *
 * @param[in] who 目标类型：
 *                - RUSAGE_SELF：当前进程
 *                - RUSAGE_CHILDREN：子进程
 *                - RUSAGE_THREAD：当前线程
 * @param[out] ru 资源使用统计结构体：
 *                - ru_utime：用户态CPU时间
 *                - ru_stime：内核态CPU时间
 *                - ru_maxrss：最大常驻集大小
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功获取统计信息。
 * @retval -EINVAL who参数无效。
 * @retval -EFAULT ru指向无效内存。
 *
 * @note 1. 统计信息包括CPU时间和内存使用。
 *       2. 支持进程和线程级统计。
 *       3. 支持子进程统计。
 *       4. 时间精度到微秒。
 */
DEFINE_SYSCALL(getrusage, (int who, struct rusage __user *ru))
{
    struct rusage kru;
    pcb_t pcb = ttosProcessSelf();
#ifdef SYS_getrusage_time64
    if (!user_access_check(ru, sizeof(long long[18]), UACCESS_W))
    {
        return -EFAULT;
    }
#else
    if (!user_access_check(ru, sizeof(long[4]), UACCESS_W))
    {
        return -EFAULT;
    }
#endif
    memset(&kru, 0, sizeof(struct rusage));
    switch (who)
    {
    case RUSAGE_SELF:
        __process_getrusage(pcb, &kru);
        break;
    case RUSAGE_CHILDREN:
        foreach_process_child(pcb, __process_getrusage, &kru);
        break;
    case RUSAGE_THREAD:
        __task_getrusage(pcb, &kru);
        break;
    default:
        return -EINVAL;
    }
#ifdef SYS_getrusage_time64
    if (copy_to_user(ru, &kru, sizeof(long long[18])) != 0)
    {
        return -EFAULT;
    }
#else
    long kru_time[4];
    kru_time[0] = kru.ru_utime.tv_sec;
    kru_time[1] = kru.ru_utime.tv_usec;
    kru_time[2] = kru.ru_stime.tv_sec;
    kru_time[3] = kru.ru_stime.tv_usec;
    if (copy_to_user(ru, kru_time, sizeof(long) * 4) != 0)
    {
        return -EFAULT;
    }
#endif
    return 0;
}