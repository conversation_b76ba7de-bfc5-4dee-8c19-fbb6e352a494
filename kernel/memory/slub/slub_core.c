#include <atomic.h>
#include <errno.h>
#include <kmalloc.h>
#include <page.h>
#include <slub.h>
#include <spinlock.h>
#include <stdlib.h>
#include <string.h>
#include <ttosMM.h>
#include <ttosUtils.h>

#define KLOG_TAG "slub"
#include <klog.h>

#undef KLOG_D
#define KLOG_D(...)

/* 全局SLUB分配器实例 */
struct slub_allocator g_slub = {
    .cache_list = NULL,
    .initialized = true,
    .cache_lock = __SPINLOCK_INITIALIZER(g_slub.cache_lock),
};

static bool percpu_cache_free_fast(struct kmem_cache *cache, void *object);
static void *percpu_cache_alloc_fast(struct kmem_cache *cache);
static int init_percpu_cache(struct kmem_cache_cpu *cpu_cache);
static void cleanup_percpu_cache(struct kmem_cache_cpu *cpu_cache);

/**
 * 计算对象在页中的数量
 */
static unsigned int calculate_objects_per_page(size_t object_size)
{
    /* 考虑对齐和元数据开销 */
    size_t usable_size = PAGE_SIZE - sizeof(void *); /* 预留一些空间给元数据 */
    unsigned int objects = usable_size / object_size;

    /* 确保至少有一个对象，即使对于大对象 */
    if (objects == 0 && object_size <= PAGE_SIZE)
    {
        objects = 1;
    }

    return objects;
}

/**
 * 初始化页中的空闲对象链表
 */
static void init_page_freelist(struct page *page, struct kmem_cache *cache)
{
    char *addr = (char *)page_address(page_to_addr(page));
    void *last = NULL;

    /* 从页末尾开始，向前构建链表 */
    for (int i = cache->objects_per_page - 1; i >= 0; i--)
    {
        void *obj = addr + i * cache->size;
        *(void **)obj = last;
        last = obj;
    }

    page->freelist = last;
    atomic_write(&page->inuse, 0);
    page->objects = cache->objects_per_page;
    page->slab_cache = cache;
    page->flags = PG_slab;
    page->next = NULL;
    spin_lock_init(&page->lock);

    KLOG_D("[SLUB_CORE] 初始化页 %llu，对象数量: %u，对象大小: %u",
           (unsigned long long)page_to_pfn(page), page->objects, cache->size);
}
/* 此接口不得在 page->lock 中调用 */
static void page_add_to_partial(struct kmem_cache *cache, struct page *page)
{
    irq_flags_t flags1, flags2;

    spin_lock_irqsave(&cache->list_lock, flags1);
    spin_lock_irqsave(&page->lock, flags2);
    page->next = cache->partial_list;
    cache->partial_list = page;
    page->flags |= PG_partial;
    spin_unlock_irqrestore(&page->lock, flags2);
    spin_unlock_irqrestore(&cache->list_lock, flags1);
}

/* 此接口必须在 cache->list_lock 中调用 */
static void page_remove_from_partial(struct kmem_cache *cache, struct page *page)
{
    irq_flags_t flags;
    spin_lock_irqsave(&page->lock, flags);
    if (cache->partial_list == page)
    {
        cache->partial_list = page->next;
        page->next = NULL;
    }
    else
    {
        struct page *p = cache->partial_list;
        while (p && p->next != page)
        {
            p = p->next;
        }
        if (p)
        {
            p->next = page->next;
        }
        page->next = NULL;
    }

    page->flags &= ~PG_partial;
    spin_unlock_irqrestore(&page->lock, flags);
}

/**
 * 从页中分配一个对象
 */
static void *alloc_from_page(struct page *page)
{
    if (!page)
    {
        return NULL;
    }
    irq_flags_t flags;
    spin_lock_irqsave(&page->lock, flags);

    if (!page->freelist)
    {
        spin_unlock_irqrestore(&page->lock, flags);
        return NULL;
    }

    void *object = page->freelist;
    page->freelist = *(void **)object;

    atomic_inc(&page->inuse);

    spin_unlock_irqrestore(&page->lock, flags);

    return object;
}

/**
 * 将对象释放回页
 */
static void free_to_page(struct page *page, void *object)
{
    if (!page || !object)
        return;

    irq_flags_t flags;
    spin_lock_irqsave(&page->lock, flags);

    /* 将对象添加到freelist头部 */
    *(void **)object = page->freelist;
    page->freelist = object;
    atomic_dec(&page->inuse);

    spin_unlock_irqrestore(&page->lock, flags);

    KLOG_D("[SLUB_CORE] 释放对象到页 %llu，使用中对象: %u/%u",
           (unsigned long long)page_to_pfn(page), atomic_read(&page->inuse), page->objects);
}

/**
 * 释放slab页
 */
static void free_slab_page(struct kmem_cache *cache, struct page *page)
{
    if (!page)
        return;

    irq_flags_t flags;

    spin_lock_irqsave(&page->lock, flags);

    /* 由于多核关系此处需要再次判断引用计数 */
    if (atomic_read(&page->inuse) != 0)
    {
        spin_unlock_irqrestore(&page->lock, flags);
        return;
    }

    KLOG_D("[SLUB_CORE] 释放缓存 %s 的页 %llu", cache->name, (unsigned long long)page_to_pfn(page));

    /* 清理页标志 */
    page->flags &= ~(PG_slab | PG_active | PG_partial);
    page->slab_cache = NULL;
    page->freelist = NULL;
    page->objects = 0;

    /* 更新缓存统计 */
    cache->page_count--;

    /* 释放页 */
    free_page(page);

    /* 此处虽然页被释放了但是由于页的元素区是一直存在的可以对锁做释放动作 */
    spin_unlock_irqrestore(&page->lock, flags);
}

static void cpu_slab_set_active_page(struct kmem_cache *cache, struct page *page)
{
    int cpu_id = cpuid_get();
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    struct page *acpage;

    irq_flags_t irqflags;

    ttos_int_lock(irqflags);

    acpage = cpu_cache->page;
    if (acpage == page)
    {
        ttos_int_unlock(irqflags);
        return;
    }
    cpu_cache->page = page;
    page->flags |= PG_active;
    page->next = NULL;

    /* 当存在旧的激活页时需要处理旧页的归属 */
    if (acpage)
    {
        irq_flags_t flags;
        spin_lock_irqsave(&acpage->lock, flags);
        acpage->flags &= ~PG_active;
        spin_unlock_irqrestore(&acpage->lock, flags);

        /* 将当前cpu的空闲内存全部归还给页 */
        while (cpu_cache->freelist)
        {
            void *object = cpu_cache->freelist;
            cpu_cache->freelist = *(void **)object;
            free_to_page(acpage, object);
        }

        ttos_int_unlock(irqflags);

        /* 如果页已完全空闲, 将页归还到页分配器 */
        if (atomic_read(&page->inuse) == 0)
        {
            free_slab_page(cache, acpage);
        }
        /* 如果有空闲对象，将其添加到partial链表 */
        else if (atomic_read(&page->inuse) < acpage->objects)
        {
            page_add_to_partial(cache, acpage);
        }
        else
        {
            /* 如果是满页 则直接悬空此页 */
            acpage->next = NULL;
        }
        return;
    }
    ttos_int_unlock(irqflags);
}

/**
 * 为缓存分配新页
 */
static struct page *allocate_slab_page(struct kmem_cache *cache)
{
    struct page *page = alloc_page();
    if (!page)
    {
        KLOG_D("[SLUB_CORE] 错误：无法分配新页给缓存 %s", cache->name);
        return NULL;
    }

    /* 初始化页作为slab */
    init_page_freelist(page, cache);

    /* 更新缓存统计 */
    cache->page_count++;

    KLOG_D("[SLUB_CORE] 为缓存 %s 分配新页 %llu", cache->name,
           (unsigned long long)page_to_pfn(page));
    return page;
}

/**
 * 慢速路径：从部分使用的页或新页分配对象
 */
static void *alloc_slow_path(struct kmem_cache *cache)
{
    struct page *page = NULL;
    void *object = NULL;
    int cpu_id = cpuid_get();
    irq_flags_t flags;
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];

    /* 1. 尝试从CPU的激活页分配 */
    object = alloc_from_page(cpu_cache->page);
    if (object)
    {
        goto out;
    }

    /* 2. 尝试从全局partial链表分配 */
    spin_lock_irqsave(&cache->list_lock, flags);
    if (cache->partial_list)
    {
        page = cache->partial_list;
        page_remove_from_partial(cache, page);
        spin_unlock_irqrestore(&cache->list_lock, flags);

        object = alloc_from_page(page);
        if (object)
        {
            /* 将页设置为激活页 */
            cpu_slab_set_active_page(cache, page);
            goto out;
        }
    }
    else
    {
        spin_unlock_irqrestore(&cache->list_lock, flags);
    }

    /* 3. 分配新页 */
    page = allocate_slab_page(cache);
    if (page)
    {
        object = alloc_from_page(page);
        /* 将页设置为激活页 */
        cpu_slab_set_active_page(cache, page);
    }

out:

    if (object)
    {
        /* 原子增加 */
        atomic_add(&cache->alloc_count, 1);
        atomic_add(&cache->slow_alloc_count, 1);
        KLOG_D("[SLUB_CORE] 慢速路径分配成功，缓存: %s，CPU: %d", cache->name, cpu_id);
    }
    else
    {
        KLOG_E("[SLUB_CORE] 慢速路径分配失败，缓存: %s", cache->name);
    }

    return object;
}

/**
 * 慢速路径：释放对象
 */
static void free_slow_path(struct kmem_cache *cache, void *object)
{
    irq_flags_t flags;
    struct page *page = ptr_to_page(object);
    if (!page)
    {
        KLOG_E("[SLUB_CORE] 错误：无法找到对象对应的页");
        return;
    }

    /* 释放对象到页 */
    free_to_page(page, object);

    /* 原子增加 */
    atomic_add(&cache->free_count, 1);

    /* 如果当前页是任何cpu的激活页则不考虑释放 */
    spin_lock_irqsave(&page->lock, flags);
    if (page->flags & PG_active)
    {
        spin_unlock_irqrestore(&page->lock, flags);
        return;
    }
    spin_unlock_irqrestore(&page->lock, flags);

    /* 如果页变为空闲，考虑释放它 */
    if (atomic_read(&page->inuse) == 0)
    {
        /* 如果当前页位于 partial_list */
        if (page->flags & PG_partial)
        {
            spin_lock_irqsave(&cache->list_lock, flags);
            /* 如果当前页是链表头就回收 不是的话 就继续留着 */
            if (cache->partial_list == page)
            {
                page_remove_from_partial(cache, page);
                spin_unlock_irqrestore(&cache->list_lock, flags);
                /* 释放空页 */
                free_slab_page(cache, page);
            }
            else
            {
                spin_unlock_irqrestore(&cache->list_lock, flags);
            }
            return;
        }

        /* 释放空页 */
        free_slab_page(cache, page);
        return;
    }

    /* 如果页部分使用，将其添加到partial链表 */
    if (atomic_read(&page->inuse) < page->objects)
    {
        page_add_to_partial(cache, page);
    }
}

/**
 * 从缓存分配对象 - 主要接口
 */
void *kmem_cache_alloc(struct kmem_cache *cache, gfp_t flags)
{
    if (!cache)
        return NULL;

    /* 快速路径：尝试从CPU缓存分配 */
    void *object = percpu_cache_alloc_fast(cache);
    if (object)
    {
        /* 原子增加 */
        atomic_add(&cache->alloc_count, 1);
        return object;
    }

    /* 慢速路径 */
    object = alloc_slow_path(cache);

    if (object && cache->ctor)
    {
        cache->ctor(object);
    }

    return object;
}

/**
 * 释放对象到缓存 - 主要接口
 */
void kmem_cache_free(struct kmem_cache *cache, void *object)
{
    if (!cache || !object)
        return;

    /* 快速路径：尝试释放到CPU缓存 */
    if (percpu_cache_free_fast(cache, object))
    {
        /* 原子增加 */
        atomic_add(&cache->free_count, 1);
        return;
    }

    /* 慢速路径 */
    free_slow_path(cache, object);
}

int kmem_cache_init(struct kmem_cache *cache, const char *name, size_t size, size_t align,
                    unsigned long flags, void (*ctor)(void *))
{
    (void)align; /* 暂时未使用对齐参数 */
    irq_flags_t irqflags;

    /* 设置基本属性 */
    strncpy(cache->name, name, sizeof(cache->name) - 1);
    cache->object_size = size;
    cache->size = (size + 7) & ~7; /* 8字节对齐 */
    cache->flags = flags;
    cache->objects_per_page = calculate_objects_per_page(cache->size);
    cache->ctor = ctor;

    atomic_write(&cache->alloc_count, 0);
    atomic_write(&cache->slow_alloc_count, 0);
    atomic_write(&cache->free_count, 0);

    /* 初始化per-CPU缓存 */
    for (int i = 0; i < CONFIG_MAX_CPUS; i++)
    {
        if (init_percpu_cache(&cache->__percpu[i]) != 0)
        {
            /* 清理已初始化的CPU缓存 */
            for (int j = 0; j < i; j++)
            {
                cleanup_percpu_cache(&cache->__percpu[j]);
            }
            free(cache);
            return -1;
        }
    }

    /* 初始化自旋锁 */
    spin_lock_init(&cache->list_lock);

    /* 添加到全局缓存链表 */
    spin_lock_irqsave(&g_slub.cache_lock, irqflags);
    cache->next = g_slub.cache_list;
    g_slub.cache_list = cache;
    spin_unlock_irqrestore(&g_slub.cache_lock, irqflags);

    KLOG_D("[SLUB_CORE] 创建缓存 '%s'，对象大小: %zu，每页对象数: %u", name, size,
           cache->objects_per_page);

    return 0;
}

/**
 * 创建新的kmem_cache
 */
struct kmem_cache *kmem_cache_create(const char *name, size_t size, size_t align,
                                     unsigned long flags, void (*ctor)(void *))
{
    if (!name || size == 0 || size > MAX_OBJECT_SIZE)
    {
        KLOG_E("[SLUB_CORE] 错误：无效的缓存参数\n");
        return NULL;
    }

    /* 分配缓存结构 */
    struct kmem_cache *cache = kzalloc(sizeof(struct kmem_cache), GFP_KERNEL);
    if (!cache)
    {
        KLOG_E("[SLUB_CORE] 错误：无法分配缓存结构\n");
        return NULL;
    }

    kmem_cache_init(cache, name, size, align, flags, ctor);

    return cache;
}

/**
 * 销毁kmem_cache
 */
void kmem_cache_destroy(struct kmem_cache *cache)
{
    if (!cache)
        return;
    irq_flags_t flags;
    KLOG_D("[SLUB_CORE] 销毁缓存 '%s'", cache->name);

    /* 从全局链表中移除 */
    spin_lock_irqsave(&g_slub.cache_lock, flags);
    struct kmem_cache **curr = &g_slub.cache_list;
    while (*curr)
    {
        if (*curr == cache)
        {
            *curr = cache->next;
            break;
        }
        curr = &(*curr)->next;
    }
    spin_unlock_irqrestore(&g_slub.cache_lock, flags);

    /* 释放所有页 */
    struct page *page = cache->partial_list;
    while (page)
    {
        struct page *next = page->next;
        free_slab_page(cache, page);
        page = next;
    }

    /* 清理per-CPU缓存 */
    for (int i = 0; i < CONFIG_MAX_CPUS; i++)
    {
        struct kmem_cache_cpu *cpu_cache = &cache->__percpu[i];
        if (cpu_cache->page)
        {
            free_slab_page(cache, cpu_cache->page);
        }
        cleanup_percpu_cache(cpu_cache);
    }

    /* 自旋锁不需要显式销毁 */

    /* 释放缓存结构 */
    free(cache);
}

/**
 * 获取缓存的对象大小
 */
size_t kmem_cache_size(struct kmem_cache *cache)
{
    return cache ? cache->object_size : 0;
}

/**
 * 初始化per-CPU缓存
 */
static int init_percpu_cache(struct kmem_cache_cpu *cpu_cache)
{
    if (!cpu_cache)
        return -1;

    /* 初始化CPU缓存结构 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->tid = 0;

    return 0;
}

/**
 * 清理per-CPU缓存
 */
static void cleanup_percpu_cache(struct kmem_cache_cpu *cpu_cache)
{
    if (!cpu_cache)
        return;

    /* 自旋锁不需要显式销毁 */

    /* 清理缓存内容 */
    cpu_cache->freelist = NULL;
    cpu_cache->page = NULL;
    cpu_cache->tid = 0;
}

/**
 * 快速路径 - 尝试从CPU缓存分配对象
 *
 * 这是SLUB分配器的核心优化：
 * 1. 首先尝试从当前CPU的freelist分配
 * 2. 如果freelist为空，尝试从当前页重新填充
 * 3. 如果都失败，返回NULL，调用者需要走慢速路径
 */
static void *percpu_cache_alloc_fast(struct kmem_cache *cache)
{
    int cpu_id = cpuid_get();
    irq_flags_t flags;
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    void *object = NULL;

    ttos_int_lock(flags);

    /* 检查freelist是否有可用对象 */
    if (cpu_cache->freelist)
    {
        object = cpu_cache->freelist;
        /* 更新freelist指针 */
        cpu_cache->freelist = *(void **)object;
        cpu_cache->tid++; /* 增加事务ID */
    }

    ttos_int_unlock(flags);

    return object;
}

/**
 * 快速路径 - 尝试释放对象到CPU缓存
 */
static bool percpu_cache_free_fast(struct kmem_cache *cache, void *object)
{
    int cpu_id = cpuid_get();
    irq_flags_t flags;
    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];

    if (ptr_to_page(object) != cpu_cache->page)
    {
        return false;
    }

    ttos_int_lock(flags);

    /* 将对象添加到freelist头部 */
    *(void **)object = cpu_cache->freelist;
    cpu_cache->freelist = object;
    cpu_cache->tid++; /* 增加事务ID */

    ttos_int_unlock(flags);

    return true;
}

/**
 * 获取CPU缓存统计信息
 */
void get_percpu_stats(struct kmem_cache *cache, int cpu_id, int *freelist_count, bool *has_page)
{
    if (cpu_id < 0 || cpu_id >= CONFIG_MAX_CPUS)
        return;

    struct kmem_cache_cpu *cpu_cache = &cache->__percpu[cpu_id];
    irq_flags_t flags;

    ttos_int_lock(flags);

    /* 计算freelist中的对象数量 */
    *freelist_count = 0;
    void *obj = cpu_cache->freelist;
    while (obj && *freelist_count < 1000)
    { /* 防止无限循环 */
        obj = *(void **)obj;
        (*freelist_count)++;
    }

    *has_page = (cpu_cache->page != NULL);

    ttos_int_unlock(flags);
}

/**
 * 获取SLUB分配器状态信息
 */
void slub_get_info(int *total_caches, int *total_pages, int *free_pages)
{
    irq_flags_t flags;
    if (!g_slub.initialized)
    {
        if (total_caches)
            *total_caches = 0;
        if (total_pages)
            *total_pages = 0;
        if (free_pages)
            *free_pages = 0;
        return;
    }

    /* 统计缓存数量 */
    int cache_count = 0;
    spin_lock_irqsave(&g_slub.cache_lock, flags);
    struct kmem_cache *cache = g_slub.cache_list;
    while (cache)
    {
        cache_count += cache->page_count;
        cache = cache->next;
    }
    spin_unlock_irqrestore(&g_slub.cache_lock, flags);

    uintptr_t total_nr, free_nr;

    page_get_info(&total_nr, &free_nr);

    if (total_caches)
        *total_caches = cache_count;
    if (total_pages)
    {
        *total_pages = total_nr;
    }
    if (free_pages)
    {
        *free_pages = free_nr;
    }
}