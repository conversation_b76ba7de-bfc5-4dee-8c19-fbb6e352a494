#include "slub.h"
#include <atomic.h>
#include <commonUtils.h>
#include <page.h>
#include <spinlock.h>
#include <stdbool.h>
#include <stdio.h>
#include <time.h>

/* 外部函数声明 */
extern void get_percpu_stats(struct kmem_cache *cache, int cpu_id, int *freelist_count,
                             bool *has_page);
extern struct slub_allocator g_slub;
/**
 * 打印页的详细信息
 */
void slub_debug_print_page(struct page *page)
{
    if (!page)
    {
        printk("页指针为NULL\n");
        return;
    }

    printk("\n--- 页信息 (ID: %llu) ---\n", (unsigned long long)page_to_pfn(page));
    printk("虚拟地址: %p\n", page_address(page_to_addr(page)));
    printk("标志位: 0x%lx", page->flags);
    if (page->flags & PG_slab)
        printk(" [SLAB]");
    if (page->flags & PG_active)
        printk(" [ACTIVE]");
    if (page->flags & PG_partial)
        printk(" [PARTIAL]");
    printk("\n");

    if (page->slab_cache)
    {
        printk("所属缓存: %s\n", page->slab_cache->name);
        printk("对象大小: %u 字节\n", page->slab_cache->size);
    }
    else
    {
        printk("所属缓存: 无\n");
    }

    printk("总对象数: %u\n", page->objects);
    printk("使用中对象: %u\n", atomic_read(&page->inuse));
    printk("空闲对象: %u\n", page->objects - atomic_read(&page->inuse));
    printk("空闲链表头: %p\n", page->freelist);
    printk("下一页: %p\n", page->next);

    /* 计算内存使用率 */
    if (page->objects > 0)
    {
        double usage = (double)atomic_read(&page->inuse) * 100.0 / page->objects;
        printk("使用率: %.1f%%\n", usage);
    }
}

/**
 * 打印缓存的详细信息
 */
void slub_debug_print_cache(struct kmem_cache *cache)
{
    if (!cache)
    {
        printk("缓存指针为NULL\n");
        return;
    }

    printk("\n=== 缓存信息: %s ===\n", cache->name);
    printk("对象大小: %u 字节 (实际: %u 字节)\n", cache->size, cache->object_size);
    printk("每页对象数: %u\n", cache->objects_per_page);
    printk("偏移量: %u\n", cache->offset);
    printk("标志位: 0x%x\n", cache->flags);
    printk("使用页数: %lu\n", cache->page_count);

    /* 统计信息 */
    printk("\n--- 统计信息 ---\n");
    printk("分配次数: %lu\n", atomic_read(&cache->alloc_count));
    printk("慢速分配次数: %lu\n", atomic_read(&cache->slow_alloc_count));
    printk("释放次数: %lu\n", atomic_read(&cache->free_count));
    printk("当前分配对象: %lu\n",
           atomic_read(&cache->alloc_count) - atomic_read(&cache->free_count));

    if (atomic_read(&cache->alloc_count) > 0)
    {
        double hit_rate =
            (double)atomic_read(&cache->free_count) * 100.0 / atomic_read(&cache->alloc_count);
        printk("释放率: %.1f%%\n", hit_rate);
        hit_rate = (double)atomic_read(&cache->slow_alloc_count) * 100.0 /
                   atomic_read(&cache->alloc_count);
        printk("慢速分配率: %.1f%%\n", hit_rate);
    }

    /* Per-CPU缓存信息 */
    printk("\n--- Per-CPU缓存状态 ---\n");
    for (int i = 0; i < CONFIG_MAX_CPUS; i++)
    {
        int freelist_count = 0;
        bool has_page = false;
        get_percpu_stats(cache, i, &freelist_count, &has_page);

        if (freelist_count > 0 || has_page)
        {
            printk("CPU %d: freelist=%d对象, page=%s\n", i, freelist_count, has_page ? "有" : "无");
        }
    }

    /* 部分使用页链表 */
    printk("\n--- 部分使用页链表 ---\n");
    struct page *page = cache->partial_list;
    int partial_count = 0;
    int total_partial_objects = 0;
    int used_partial_objects = 0;

    while (page && partial_count < 10)
    { /* 最多显示10页 */
        printk("页 %llu: %u/%u 对象使用中\n", (unsigned long long)page_to_pfn(page),
               atomic_read(&page->inuse), page->objects);
        total_partial_objects += page->objects;
        used_partial_objects += atomic_read(&page->inuse);
        page = page->next;
        partial_count++;
    }

    if (partial_count > 0)
    {
        printk("部分使用页数: %d\n", partial_count);
        printk("部分页总对象: %d\n", total_partial_objects);
        printk("部分页使用对象: %d\n", used_partial_objects);
        if (total_partial_objects > 0)
        {
            double partial_usage = (double)used_partial_objects * 100.0 / total_partial_objects;
            printk("部分页使用率: %.1f%%\n", partial_usage);
        }
    }
    else
    {
        printk("无部分使用页\n");
    }

    if (page)
    {
        printk("... (还有更多页)\n");
    }
}

/**
 * 打印所有缓存的统计信息
 */
void slub_print_stats(void)
{
    if (!g_slub.initialized)
    {
        printk("SLUB分配器未初始化\n");
        return;
    }
    printk("\n");
    printk("╔══════════════════════════════════════════════════════════════╗\n");
    printk("║                    SLUB分配器统计信息                        ║\n");
    printk("╚══════════════════════════════════════════════════════════════╝\n");

    /* 全局信息 */
    int total_caches, total_pages;
    int total_memory;
    slub_get_info(&total_caches, &total_pages, &total_memory);

    printk("\n--- 全局统计 ---\n");
    printk("总缓存数: %d\n", total_caches);
    printk("使用页数: %d\n", total_pages);
    printk("空闲内存: %zu KB (%.1f MB)\n", (uint64_t)(total_memory << PAGE_SIZE_SHIFT) / 1024,
           (double)(uint64_t)(total_memory << PAGE_SIZE_SHIFT) / (1024 * 1024));

    /* 遍历所有缓存 */
    printk("\n--- 缓存信息 ---\n");
    struct kmem_cache *cache = g_slub.cache_list;
    int cache_index = 0;

    while (cache)
    {
        printk("\n[%d] 缓存: %-20s | 大小: %4u | 页数: %3lu | 分配: %8lu | 释放: %8lu\n",
               cache_index++, cache->name, cache->size, cache->page_count,
               atomic_read(&cache->alloc_count), atomic_read(&cache->free_count));

        /* 计算内存使用 */
        size_t cache_memory = cache->page_count * PAGE_SIZE;
        size_t object_memory =
            (atomic_read(&cache->alloc_count) - atomic_read(&cache->free_count)) * cache->size;
        if (cache_memory > 0)
        {
            double efficiency = (double)object_memory * 100.0 / cache_memory;
            printk("    内存: %zu KB, 对象内存: %zu KB, 效率: %.1f%%\n", cache_memory / 1024,
                   object_memory / 1024, efficiency);
        }

        cache = cache->next;
    }

    printk("\n");

    /* 遍历所有缓存 */
    printk("\n--- 缓存详情 ---\n");
    cache = g_slub.cache_list;

    while (cache)
    {
        slub_debug_print_cache(cache);

        cache = cache->next;
    }

    printk("\n");
}

/**
 * 打印内存碎片信息
 */
void slub_print_fragmentation(void)
{
    if (!g_slub.initialized)
    {
        printk("SLUB分配器未初始化\n");
        return;
    }

    printk("\n=== 内存碎片分析 ===\n");

    size_t total_allocated_memory = 0;
    size_t total_used_memory = 0;
    size_t total_wasted_memory = 0;
    int total_pages = 0;
    int total_partial_pages = 0;

    struct kmem_cache *cache = g_slub.cache_list;

    while (cache)
    {
        size_t cache_allocated = cache->page_count * PAGE_SIZE;
        size_t cache_used =
            (atomic_read(&cache->alloc_count) - atomic_read(&cache->free_count)) * cache->size;
        size_t cache_wasted = cache_allocated - cache_used;

        total_allocated_memory += cache_allocated;
        total_used_memory += cache_used;
        total_wasted_memory += cache_wasted;
        total_pages += cache->page_count;

        /* 统计部分使用页 */
        struct page *page = cache->partial_list;
        int partial_pages = 0;
        while (page)
        {
            partial_pages++;
            page = page->next;
        }
        total_partial_pages += partial_pages;

        if (cache->page_count > 0)
        {
            double fragmentation = (double)cache_wasted * 100.0 / cache_allocated;
            printk("缓存 %-20s: 碎片率 %5.1f%%, 部分页 %3d\n", cache->name, fragmentation,
                   partial_pages);
        }

        cache = cache->next;
    }

    printk("\n--- 总体碎片情况 ---\n");
    printk("总分配内存: %zu KB\n", total_allocated_memory / 1024);
    printk("实际使用内存: %zu KB\n", total_used_memory / 1024);
    printk("浪费内存: %zu KB\n", total_wasted_memory / 1024);
    printk("总页数: %d\n", total_pages);
    printk("部分使用页数: %d\n", total_partial_pages);

    if (total_allocated_memory > 0)
    {
        double overall_fragmentation = (double)total_wasted_memory * 100.0 / total_allocated_memory;
        printk("总体碎片率: %.1f%%\n", overall_fragmentation);
    }

    if (total_pages > 0)
    {
        double partial_ratio = (double)total_partial_pages * 100.0 / total_pages;
        printk("部分页比例: %.1f%%\n", partial_ratio);
    }
}

/**
 * 验证内存完整性
 */
bool slub_verify_integrity(void)
{
    if (!g_slub.initialized)
    {
        printk("SLUB分配器未初始化\n");
        return false;
    }

    printk("\n=== 内存完整性检查 ===\n");
    bool integrity_ok = true;

    struct kmem_cache *cache = g_slub.cache_list;

    while (cache)
    {
        printk("检查缓存: %s\n", cache->name);

        /* 检查分配/释放计数 */
        if (atomic_read(&cache->free_count) > atomic_read(&cache->alloc_count))
        {
            printk("  错误：释放次数(%lu) > 分配次数(%lu)\n", cache->free_count,
                   cache->alloc_count);
            integrity_ok = false;
        }

        /* 检查页链表 */
        struct page *page = cache->partial_list;
        int page_count = 0;
        while (page && page_count < 1000)
        { /* 防止无限循环 */
            if (page->slab_cache != cache)
            {
                printk("  错误：页 %llu 的缓存指针不匹配\n", (unsigned long long)page_to_pfn(page));
                integrity_ok = false;
            }

            if (atomic_read(&page->inuse) > page->objects)
            {
                printk("  错误：页 %llu 使用对象数(%u) > 总对象数(%u)\n",
                       (unsigned long long)page_to_pfn(page), atomic_read(&page->inuse),
                       page->objects);
                integrity_ok = false;
            }

            page = page->next;
            page_count++;
        }

        if (page_count >= 1000)
        {
            printk("  警告：页链表可能存在循环\n");
            integrity_ok = false;
        }

        cache = cache->next;
    }

    if (integrity_ok)
    {
        printk("内存完整性检查通过\n");
    }
    else
    {
        printk("内存完整性检查失败！\n");
    }

    return integrity_ok;
}

/**
 * 导出调试信息到文件
 */
static int slub_export_debug_info(int argc, const char *argv[])
{
    /* 输出所有统计信息 */
    slub_print_stats();
    slub_print_fragmentation();
    slub_verify_integrity();
}

#if CONFIG_SHELL
#include <shell.h>
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_MAIN) |
                     SHELL_CMD_DISABLE_RETURN,
                 slub, slub_export_debug_info, list slub);
#endif /* CONFIG_SHELL */
