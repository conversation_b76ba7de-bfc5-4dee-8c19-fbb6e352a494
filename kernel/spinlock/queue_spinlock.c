/**
 * @file    kernel/spinlock/queue_spinlock.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 8c83d7de 2024-07-02 移除头文件路径中的linux
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * *************-03-27 移除日志中\n 全部修改为日志输出
 * b1305a1f 2024-03-27 修复spinlock单核死锁问题
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 43b302d7 2024-03-13 添加中断、自旋锁、原子操作相关功能实现。
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/************************头 文 件******************************/
#include <atomic.h>
#include <barrier.h>
#include <cpuid.h>
#include <spinlock.h>
#include <stdio.h>
#include <system/compiler.h>
#include <ttos.h>

#define KLOG_TAG "Kernel"
#include <klog.h>

/************************宏 定 义******************************/
#define SPINLOCK_TIMEOUT_MS 1000

/************************类型定义******************************/

/************************外部声明******************************/
void backtrace_r(const char *cookie, uintptr_t frame_address);

/************************前向声明******************************/

/************************模块变量******************************/

/************************全局变量******************************/
DEFINE_SPINLOCK(ttosKernelLockVar);

DEFINE_SPINLOCK(deadlock_spinlock);

/************************函数实现******************************/
static void deadlock_check(T_UDWORD start_count)
{
    T_UDWORD now_count = TTOS_GetCurrentSystemCount();
    T_UDWORD wait_ms = TTOS_GetCurrentSystemSubTime(start_count, now_count, TTOS_MS_UNIT);

    if (wait_ms > SPINLOCK_TIMEOUT_MS)
    {
        irq_flags_t flags;

        spin_lock_irqsave(&deadlock_spinlock, flags);

        printk("spinlock may deadlock!\n");
        uintptr_t fp = (uintptr_t)__builtin_frame_address(0);
        if (fp)
        {
            backtrace_r("deadlock", fp);
        }

        printk("while(1) at %s:%d\n", __FILE__, __LINE__);
        spin_unlock_irqrestore(&deadlock_spinlock, flags);
        while (1)
            ;
    }
}

/*
 * @brief:
 *     获取自旋锁
 * @param[in] lock 自旋锁对象
 * @retval 无
 */
void kernel_spin_lock(ttos_spinlock_t *lock)
{
    s32 cpu_id;
    int ticket;
    size_t flags = 0;

    ttos_int_lock(flags);

    /* 获取当前CPU号 */
    cpu_id = cpuid_get();

    /* 如果自旋锁嵌套获取则只增加嵌套计数 */
    if (likely(lock->cpu_id != cpu_id))
    {
        u64 start_count = TTOS_GetCurrentSystemCount();

        /* 获取当前票号 */
        ticket = atomic_inc_return(&lock->ticket);

        /* 自旋等待服务票号是当前票号 */
        while (ticket != atomic_read(&lock->ticked_served))
        {
            deadlock_check(start_count);
        }

        /* 设置自旋锁拥有者CPU */
        lock->cpu_id = cpu_id;
    }

    /* 内存栅栏，同步以上操作 */
    smp_mb();

    /* 增加自旋锁嵌套计数 */
    lock->nest_count++;

    ttos_int_unlock(flags);
}

/*
 * @brief:
 *     尝试获取自旋锁
 * @param[in] lock 自旋锁对象
 * @retval true 获取成功
 *         false 获取失败
 */
bool kernel_spin_trylock(ttos_spinlock_t *lock)
{
    s32 cpu_id;
    int ticket;
    size_t flags = 0;

    ttos_int_lock(flags);

    /* 获取当前CPU号 */
    cpu_id = cpuid_get();

    /* 如果自旋锁嵌套获取则只增加嵌套计数 */
    if (likely(lock->cpu_id != cpu_id))
    {
        /* 获取当前票号 */
        // ticket = atomic_inc_return(&lock->ticket);
        ticket = atomic_read(&lock->ticket);

        if ((ticket + 1) != atomic_read(&lock->ticked_served))
        {
            return false;
        }

        if (!atomic32_compare_exchange((volatile s32 *)&lock->ticket.counter, &ticket, ticket + 1))
        {
            return false;
        }

        /* 设置自旋锁拥有者CPU */
        lock->cpu_id = cpu_id;
    }

    /* 内存栅栏，同步以上操作 */
    smp_mb();

    /* 增加自旋锁嵌套计数 */
    lock->nest_count++;

    ttos_int_unlock(flags);

    return true;
}

/*
 * @brief:
 *     释放自旋锁
 * @param[in] lock 自旋锁对象
 * @retval 无
 */
void kernel_spin_unlock(ttos_spinlock_t *lock)
{
    u32 cpu_id;
    size_t flags = 0;

    ttos_int_lock(flags);

    /* 获取当前CPU号 */
    cpu_id = cpuid_get();

    /* 拥有者与释放者必须是同一CPU */
    if (lock->cpu_id == cpu_id)
    {
        /* 判断内核锁是否嵌套 */
        if (likely(--lock->nest_count == 0))
        {
            /* 设置当前获取锁的CPU */
            lock->cpu_id = TTOS_CPU_NONE;

            /* 内存栅栏，同步以上操作 */
            smp_mb();

            /* 增加服务票号 */
            atomic_inc(&lock->ticked_served);
        }
    }
    else
    {
        /* 调用内核故障打印接口 */
        KLOG_E("kernel_spin_unlock panic cpu_id:%d", cpu_id);
    }

    ttos_int_unlock(flags);
}
