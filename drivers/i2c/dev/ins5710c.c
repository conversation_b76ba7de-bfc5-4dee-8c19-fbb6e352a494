#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/i2c/i2c.h>
#include <driver/of.h>
#include <errno.h>
#include <fcntl.h>
#include <fs/fs.h>
#include <fs/ioctl.h>
#include <inttypes.h>
#include <stdio.h>
#include <time/ktime.h>
#include <ttos_init.h>

#undef KLOG_TAG
#define KLOG_TAG "sl-rtc"
#include "bcd.h"
#include <klog.h>
#include <time.h>

#undef KLOG_D
#define KLOG_D(...)

#define RTC_RD_TIME _IOR('p', 0x09, struct tm)  /* Read RTC time   */
#define RTC_SET_TIME _IOW('p', 0x0a, struct tm) /* Set RTC time    */

#define RTC_REG_SEC 0x00
#define RTC_REG_MIN 0x01
#define RTC_REG_HOUR 0x02
#define RTC_REG_WDAY 0x03
#define RTC_REG_MDAY 0x04
#define RTC_REG_MONTH 0x05
#define RTC_REG_YEAR 0x06

#define RTC_REG_FLAG 0x0e
#define RTC_BIT_FLAG_VDET (1 << 0)
#define RTC_BIT_FLAG_VLF (1 << 1)

struct rtc_priv_s
{
    struct i2c_device *dev;
    uint32_t addr;
    uint32_t offset;
    uint32_t instance_id;
};

static int frtc_open(struct file *filep);
static int frtc_close(struct file *filep);
static ssize_t frtc_read(struct file *filep, char *buffer, size_t buflen);
static ssize_t frtc_write(struct file *filep, const char *buffer, size_t buflen);
static int frtc_ioctl(struct file *filep, unsigned int cmd, unsigned long arg);
static off_t frtc_lseek(struct file *filep, off_t offset, int whence);

static const struct file_operations fops = {
    .open = frtc_open,
    .close = frtc_close,
    .read = frtc_read,
    .write = frtc_write,
    .ioctl = frtc_ioctl,
    .seek = frtc_lseek,
};

static struct rtc_priv_s *gpriv;

static int ins5710c_init(struct file *filep)
{
    uint8_t buf;
    struct i2c_device *dev = filep->f_inode->i_private;
    int ret;
    uint8_t txBuf[2] = {RTC_REG_FLAG};

    if (NULL == filep)
    {
        return -(EPERM);
    }

    ret = i2c_write_read(dev, dev->addr, txBuf, 1, &buf, sizeof(buf));
    if (ret != 0)
    {
        KLOG_E("i2c read failed");
        return -ENODEV;
    }

    if ((buf & RTC_BIT_FLAG_VLF) | (buf & RTC_BIT_FLAG_VDET))
    {
        buf &= ~(RTC_BIT_FLAG_VLF | RTC_BIT_FLAG_VDET);
        txBuf[0] = RTC_REG_FLAG;
        txBuf[1] = buf;
        ret = i2c_write(dev, txBuf, sizeof(txBuf), dev->addr);
        if (ret != 0)
        {
            KLOG_E("i2c write failed");
            return ret;
        }
    }

    return 0;
}

static int frtc_open(struct file *filep)
{
    struct i2c_device *dev = filep->f_inode->i_private;
    struct rtc_priv_s *priv = dev->parent.priv;
    UNUSED_ARG(priv);
    KLOG_D("RTC %d opened", priv->instance_id);

    return ins5710c_init(filep);
}

static int frtc_close(struct file *filep)
{
    struct i2c_device *dev = filep->f_inode->i_private;
    struct rtc_priv_s *priv = dev->parent.priv;
    UNUSED_ARG(priv);
    KLOG_D("RTC %d closed", priv->instance_id);

    return 0;
}

static ssize_t frtc_read(struct file *filep, char *buffer, size_t buflen)
{
    struct i2c_device *dev = filep->f_inode->i_private;
    struct rtc_priv_s *priv = dev->parent.priv;
    char tx_buf[2] = {0};
    int ret;

    tx_buf[0] = priv->offset & 0xff;
    ret = i2c_write_read(dev, dev->addr, tx_buf, 1, buffer, buflen);
    if (ret != 0)
    {
        KLOG_E("i2c read failed");
        return -1;
    }
    KLOG_D("RTC %d read", priv->instance_id);

    return buflen;
}

static ssize_t frtc_write(struct file *filep, const char *buffer, size_t buflen)
{
    struct i2c_device *dev = NULL;
    struct rtc_priv_s *priv = NULL;
    int ret;

    if (NULL == filep)
    {
        return -(EINVAL);
    }

    dev = filep->f_inode->i_private;
    if (NULL == dev)
    {
        return -(EINVAL);
    }
    priv = dev->parent.priv;
    UNUSED_ARG(priv);
    ret = i2c_write(dev, (const uint8_t *)buffer, buflen, dev->addr);
    if (ret != 0)
    {
        KLOG_E("i2c write failed");
        return -1;
    }

    KLOG_D("RTC %d write", priv->instance_id);
    return buflen;
}

int checkDate(int year, int month, int day)
{
    int y = 0;

    if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 ||
        month == 12)
    {
        y = 31;
    }

    if (month == 4 || month == 6 || month == 9 || month == 11)
    {
        y = 30;
    }

    if (month == 2)
    {
        if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0)
        {
            y = 29;
        }
        else
        {
            y = 28;
        }
    }

    if (month < 1 || month > 12)
    {
        return 0;
    }
    else if (y == 31 && (day < 1 || day > 31))
    {
        return 0;
    }
    else if ((y == 30) && (day < 1 || day > 30))
    {
        return 0;
    }
    else if ((y == 29) && (day < 1 || day > 29))
    {
        return 0;
    }
    else if ((y == 28) && (day < 1 || day > 28))
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

int checkTime(int h, int m, int s)
{
    if (h < 0 || h > 23)
        return 0;
    else if (m < 0 || m > 59)
        return 0;
    else if (s < 0 || s > 59)
        return 0;
    else
        return 1;
}

static int ins5710_rtc_set_time(struct file *filep, struct tm *tm)
{
    int ret;
    struct i2c_device *dev = filep->f_inode->i_private;
    u8 buf[8] = {0};

    if (checkDate(tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday) != 1)
    {
        return -EINVAL;
    }
    else if (checkTime(tm->tm_hour, tm->tm_min, tm->tm_sec) != 1)
    {
        return -EINVAL;
    }

    /* Years >= 2100 are to far in the future, 19XX is to early */
    if (tm->tm_year < 100 || tm->tm_year >= 200)
    {
        return -EINVAL;
    }

    buf[0] = RTC_REG_SEC;
    buf[RTC_REG_SEC + 1] = bin2bcd(tm->tm_sec);
    buf[RTC_REG_MIN + 1] = bin2bcd(tm->tm_min);
    buf[RTC_REG_HOUR + 1] = bin2bcd(tm->tm_hour);
    buf[RTC_REG_WDAY + 1] = bin2bcd(tm->tm_wday);
    buf[RTC_REG_MDAY + 1] = bin2bcd(tm->tm_mday);
    buf[RTC_REG_MONTH + 1] = bin2bcd(tm->tm_mon + 1);
    buf[RTC_REG_YEAR + 1] = bin2bcd(tm->tm_year - 100);

    ret = i2c_write(dev, buf, sizeof(buf), dev->addr);
    if (ret != 0)
    {
        KLOG_E("i2c write failed");
        return -1;
    }

    // struct timespec64 ts = clock_tm_to_timespec64(tm);
    // kernel_clock_settime(CLOCK_REALTIME, &ts);

    return 0;
}

static int ins5710_rtc_read_time(struct file *filep, struct tm *tm)
{
    uint8_t buf[7] = {0};
    struct i2c_device *dev = NULL;
    int ret;
    uint8_t txBuf[2] = {0};

    if (NULL == filep)
    {
        return -EINVAL;
    }

    dev = filep->f_inode->i_private;
    ret = i2c_write_read(dev, dev->addr, txBuf, 1, buf, sizeof(buf));
    if (ret != 0)
    {
        KLOG_E("i2c read failed");
    }

    tm->tm_sec = bcd2bin(buf[RTC_REG_SEC] & 0x7f);
    tm->tm_min = bcd2bin(buf[RTC_REG_MIN] & 0x7f);
    tm->tm_hour = bcd2bin(buf[RTC_REG_HOUR] & 0x3f);
    tm->tm_wday = bcd2bin(buf[RTC_REG_WDAY] & 0x07);
    tm->tm_mday = bcd2bin(buf[RTC_REG_MDAY] & 0x3f);
    tm->tm_mon = bcd2bin(buf[RTC_REG_MONTH] &= 0x1f) - 1;
    tm->tm_year = bcd2bin(buf[RTC_REG_YEAR]) + 100;

    // struct timespec64 ts = clock_tm_to_timespec64(tm);
    // kernel_clock_settime(CLOCK_REALTIME, &ts);

    return 0;
}

static int frtc_ioctl(struct file *filep, unsigned int cmd, unsigned long arg)
{
    struct i2c_device *dev = filep->f_inode->i_private;
    struct rtc_priv_s *priv = dev->parent.priv;
    int ret = 0;

    struct tm *time_p = (struct tm *)arg;
    switch ((uint32_t)cmd)
    {
    case RTC_SET_TIME:
        ret = ins5710_rtc_set_time(filep, time_p);
        break;
    case RTC_RD_TIME:
        ret = ins5710_rtc_read_time(filep, time_p);
        break;
    }
    UNUSED_ARG(priv);
    KLOG_D("RTC %d ioctl", priv->instance_id);

    return ret;
}

static off_t frtc_lseek(struct file *filep, off_t offset, int whence)
{
    struct i2c_device *dev = filep->f_inode->i_private;
    struct rtc_priv_s *priv = dev->parent.priv;

    off_t new_offset;

    switch (whence)
    {
    case SEEK_SET:
        new_offset = offset;
        break;

    case SEEK_CUR:
        new_offset = priv->offset + offset;
        break;

    case SEEK_END:
    default:
        return -EINVAL;
    }

    if (new_offset < 0)
    {
        return -EINVAL;
    }

    priv->offset = new_offset;
    KLOG_D("RTC %d lseek to %" PRId64, priv->instance_id, new_offset);

    return new_offset;
}

static int i2c_rtc_driver_probe(struct device *dev)
{
    struct i2c_device *i2cdev = (struct i2c_device *)(dev);

    int ret;

    gpriv = (struct rtc_priv_s *)calloc(1, sizeof(struct rtc_priv_s));
    if (gpriv == NULL)
    {
        KLOG_E("No enough memory");
        return -ENOMEM;
    }

    gpriv->dev = i2cdev;
    gpriv->dev->parent.priv = gpriv;
    gpriv->offset = 0;

    ret = of_property_read_u32_array(dev->of_node, "reg", &gpriv->addr, 1);
    if (ret)
    {
        goto errout;
    }

    i2cdev->addr = gpriv->addr & 0xffff;

    KLOG_D("%s dev: %s  parent bus: %s", __func__, dev->driver->name, dev->bus->name);
    KLOG_D("i2c dev addr: 0x%x", i2cdev->addr);

    i2c_device_bind_path(dev, &fops, 0666);
    return 0;

errout:
    free(gpriv);
    return -1;
}

static struct of_device_id i2c_rtc_table[] = {
    {.compatible = "ins5710c,rtc"},
    {.compatible = "ins5699s,rtc"},
    {/* end of list */},
};

static struct driver i2c_rtc_driver = {
    .name = "rtc",
    .probe = i2c_rtc_driver_probe,
    .match_table = &i2c_rtc_table[0],
};

static int i2c_rtc_init(void)
{
    return i2c_add_driver(&i2c_rtc_driver);
}
INIT_EXPORT_SERVE_FS(i2c_rtc_init, "i2c rtc driver init");
